-- Migration: Add customer-specific collection access
-- This allows generating unique links for collections tied to specific customers

-- Customer collection access table
CREATE TABLE IF NOT EXISTS customer_collection_access (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    collection_id UUID NOT NULL REFERENCES collections(id) ON DELETE CASCADE,
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    access_token VARCHAR(255) UNIQUE NOT NULL, -- Unique token for accessing the collection
    customer_phone VARCHAR(20) NOT NULL, -- Denormalized for quick lookup
    expires_at TIMESTAMPTZ, -- Optional expiration date
    is_active BOOLEAN DEFAULT TRUE,
    access_count INTEGER DEFAULT 0, -- Track how many times the link was accessed
    last_accessed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    -- Ensure one active token per customer-collection pair
    UNIQUE(collection_id, customer_id, is_active) DEFERRABLE INITIALLY DEFERRED
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_customer_collection_access_token ON customer_collection_access(access_token);
CREATE INDEX IF NOT EXISTS idx_customer_collection_access_collection_id ON customer_collection_access(collection_id);
CREATE INDEX IF NOT EXISTS idx_customer_collection_access_customer_id ON customer_collection_access(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_collection_access_phone ON customer_collection_access(customer_phone);
CREATE INDEX IF NOT EXISTS idx_customer_collection_access_active ON customer_collection_access(is_active);

-- Function to generate secure random tokens
CREATE OR REPLACE FUNCTION generate_access_token() RETURNS VARCHAR(255) AS $$
BEGIN
    RETURN encode(gen_random_bytes(32), 'base64url');
END;
$$ LANGUAGE plpgsql;

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at
CREATE TRIGGER update_customer_collection_access_updated_at
    BEFORE UPDATE ON customer_collection_access
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
