-- Migration: Add share_url_id column for unique shareable URLs
-- This allows generating unique URLs that are separate from access tokens

-- Add the share_url_id column
ALTER TABLE customer_collection_access 
ADD COLUMN share_url_id VARCHAR(32) UNIQUE;

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_customer_collection_access_share_url_id ON customer_collection_access(share_url_id);

-- Enable pgcrypto extension for gen_random_bytes
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Function to generate unique share URL identifiers
CREATE OR REPLACE FUNCTION generate_share_url_id() RETURNS VARCHAR(32) AS $$
BEGIN
    RETURN encode(gen_random_bytes(16), 'hex');
END;
$$ LANGUAGE plpgsql;

-- Update existing records with unique share_url_id values
UPDATE customer_collection_access
SET share_url_id = generate_share_url_id()
WHERE share_url_id IS NULL;

-- Make the column NOT NULL after populating existing records
ALTER TABLE customer_collection_access 
ALTER COLUMN share_url_id SET NOT NULL;
