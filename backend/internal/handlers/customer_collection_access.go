package handlers

import (
	"net/http"

	"github.com/anandjewels/jewelry-backend/internal/database"
	"github.com/anandjewels/jewelry-backend/internal/middleware"
	"github.com/anandjewels/jewelry-backend/internal/models"
	"github.com/anandjewels/jewelry-backend/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// CustomerCollectionAccessHandler handles customer collection access HTTP requests
type CustomerCollectionAccessHandler struct {
	db                              *database.DB
	customerCollectionAccessService *services.CustomerCollectionAccessService
	collectionService               *services.CollectionService
}

// NewCustomerCollectionAccessHandler creates a new customer collection access handler
func NewCustomerCollectionAccessHandler(db *database.DB) *CustomerCollectionAccessHandler {
	return &CustomerCollectionAccessHandler{
		db:                              db,
		customerCollectionAccessService: services.NewCustomerCollectionAccessService(db),
		collectionService:               services.NewCollectionService(db),
	}
}

// CreateCustomerCollectionAccess creates a new customer collection access token
// @Summary Create customer collection access
// @Description Generate a unique access token for a customer to access a specific collection
// @Tags customer-collection-access
// @Accept json
// @Produce json
// @Param access body models.CreateCustomerCollectionAccessRequest true "Access data"
// @Success 201 {object} models.CustomerCollectionAccessResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/collections/{id}/customer-access [post]
func (h *CustomerCollectionAccessHandler) CreateCustomerCollectionAccess(c *gin.Context) {
	collectionIDStr := c.Param("id")
	collectionID, err := uuid.Parse(collectionIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_collection_id",
			Message: "Invalid collection ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	var req models.CreateCustomerCollectionAccessRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_request",
			Message: "Invalid request body",
			Code:    http.StatusBadRequest,
			Details: err.Error(),
		})
		return
	}

	// Set collection ID from URL parameter
	req.CollectionID = collectionID

	access, err := h.customerCollectionAccessService.CreateCustomerCollectionAccess(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "creation_failed",
			Message: "Failed to create customer collection access",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, access.ToResponse())
}

// GetCollectionByAccessToken retrieves a collection using customer access token
// @Summary Get collection by access token
// @Description Retrieve a collection using a customer-specific access token
// @Tags customer-collection-access
// @Accept json
// @Produce json
// @Param token path string true "Access Token"
// @Success 200 {object} models.CollectionResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 404 {object} middleware.ErrorResponse
// @Failure 403 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/collections/shared/{token} [get]
func (h *CustomerCollectionAccessHandler) GetCollectionByAccessToken(c *gin.Context) {
	token := c.Param("token")
	if token == "" {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "missing_token",
			Message: "Access token is required",
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Get access record
	access, err := h.customerCollectionAccessService.GetCustomerCollectionAccessByToken(token)
	if err != nil {
		if err.Error() == "access token not found" {
			c.JSON(http.StatusNotFound, middleware.ErrorResponse{
				Error:   "token_not_found",
				Message: "Access token not found",
				Code:    http.StatusNotFound,
			})
			return
		}
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "fetch_failed",
			Message: "Failed to validate access token",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	// Check if access is valid
	if !access.IsValid() {
		c.JSON(http.StatusForbidden, middleware.ErrorResponse{
			Error:   "access_denied",
			Message: "Access token is expired or inactive",
			Code:    http.StatusForbidden,
		})
		return
	}

	// Get the collection
	collection, err := h.collectionService.GetCollectionByID(access.CollectionID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "fetch_failed",
			Message: "Failed to fetch collection",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	// Increment access count
	_ = h.customerCollectionAccessService.IncrementAccessCount(token)

	c.JSON(http.StatusOK, collection.ToResponse())
}

// GetCustomerCollectionAccesses retrieves all access tokens for a collection
// @Summary Get customer collection accesses
// @Description Get all customer access tokens for a specific collection
// @Tags customer-collection-access
// @Accept json
// @Produce json
// @Param id path string true "Collection ID"
// @Success 200 {array} models.CustomerCollectionAccessResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/collections/{id}/customer-access [get]
func (h *CustomerCollectionAccessHandler) GetCustomerCollectionAccesses(c *gin.Context) {
	collectionIDStr := c.Param("id")
	collectionID, err := uuid.Parse(collectionIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_collection_id",
			Message: "Invalid collection ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	accesses, err := h.customerCollectionAccessService.GetCustomerCollectionAccessesByCollection(collectionID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "fetch_failed",
			Message: "Failed to fetch customer collection accesses",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	// Convert to response format
	responses := make([]*models.CustomerCollectionAccessResponse, len(accesses))
	for i, access := range accesses {
		responses[i] = access.ToResponse()
	}

	c.JSON(http.StatusOK, responses)
}

// RevokeCustomerCollectionAccess deletes a customer collection access token
// @Summary Delete customer collection access
// @Description Permanently delete a customer's access token for a collection
// @Tags customer-collection-access
// @Accept json
// @Produce json
// @Param id path string true "Collection ID"
// @Param accessId path string true "Access ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/collections/{id}/customer-access/{accessId} [delete]
func (h *CustomerCollectionAccessHandler) RevokeCustomerCollectionAccess(c *gin.Context) {
	accessIDStr := c.Param("accessId")
	accessID, err := uuid.Parse(accessIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_access_id",
			Message: "Invalid access ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	err = h.customerCollectionAccessService.RevokeCustomerCollectionAccess(accessID)
	if err != nil {
		if err.Error() == "customer collection access not found" {
			c.JSON(http.StatusNotFound, middleware.ErrorResponse{
				Error:   "not_found",
				Message: "Customer collection access not found",
				Code:    http.StatusNotFound,
			})
			return
		}

		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "delete_failed",
			Message: "Failed to delete customer collection access",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":   "Customer collection access deleted successfully",
		"access_id": accessID,
	})
}
