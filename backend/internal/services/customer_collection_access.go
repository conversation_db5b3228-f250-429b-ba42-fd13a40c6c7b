package services

import (
	"crypto/rand"
	"database/sql"
	"fmt"
	"time"

	"github.com/anandjewels/jewelry-backend/internal/database"
	"github.com/anandjewels/jewelry-backend/internal/models"
	"github.com/google/uuid"
)

// CustomerCollectionAccessService handles customer collection access operations
type CustomerCollectionAccessService struct {
	db *database.DB
}

// NewCustomerCollectionAccessService creates a new customer collection access service
func NewCustomerCollectionAccessService(db *database.DB) *CustomerCollectionAccessService {
	return &CustomerCollectionAccessService{db: db}
}

// generateAccessToken generates a 4-digit OTP-style access token
func (s *CustomerCollectionAccessService) generateAccessToken() (string, error) {
	// Generate a random number between 0000 and 9999
	bytes := make([]byte, 2)
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.Errorf("failed to generate random bytes: %w", err)
	}

	// Convert to 4-digit string with leading zeros
	num := int(bytes[0])<<8 | int(bytes[1])
	token := fmt.Sprintf("%04d", num%10000)
	return token, nil
}

// CreateCustomerCollectionAccess creates a new customer collection access token
func (s *CustomerCollectionAccessService) CreateCustomerCollectionAccess(req *models.CreateCustomerCollectionAccessRequest) (*models.CustomerCollectionAccess, error) {
	// Generate unique access token
	accessToken, err := s.generateAccessToken()
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	// Deactivate any existing active tokens for this customer-collection pair
	deactivateQuery := `
		UPDATE customer_collection_access 
		SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP
		WHERE collection_id = $1 AND customer_id = $2 AND is_active = TRUE
	`
	_, err = s.db.Postgres.Exec(deactivateQuery, req.CollectionID, req.CustomerID)
	if err != nil {
		return nil, fmt.Errorf("failed to deactivate existing tokens: %w", err)
	}

	access := &models.CustomerCollectionAccess{
		ID:            uuid.New(),
		CollectionID:  req.CollectionID,
		CustomerID:    req.CustomerID,
		AccessToken:   accessToken,
		CustomerPhone: req.CustomerPhone,
		ExpiresAt:     req.ExpiresAt,
		IsActive:      true,
		AccessCount:   0,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	query := `
		INSERT INTO customer_collection_access (
			id, collection_id, customer_id, access_token, customer_phone,
			expires_at, is_active, access_count, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10
		)
	`

	_, err = s.db.Postgres.Exec(query,
		access.ID, access.CollectionID, access.CustomerID, access.AccessToken,
		access.CustomerPhone, access.ExpiresAt, access.IsActive, access.AccessCount,
		access.CreatedAt, access.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create customer collection access: %w", err)
	}

	return access, nil
}

// GetCustomerCollectionAccessByToken retrieves access by token
func (s *CustomerCollectionAccessService) GetCustomerCollectionAccessByToken(token string) (*models.CustomerCollectionAccess, error) {
	query := `
		SELECT cca.id, cca.collection_id, cca.customer_id, cca.access_token,
			   cca.customer_phone, cca.expires_at, cca.is_active, cca.access_count,
			   cca.last_accessed_at, cca.created_at, cca.updated_at
		FROM customer_collection_access cca
		WHERE cca.access_token = $1
	`

	var access models.CustomerCollectionAccess
	err := s.db.Postgres.QueryRow(query, token).Scan(
		&access.ID, &access.CollectionID, &access.CustomerID, &access.AccessToken,
		&access.CustomerPhone, &access.ExpiresAt, &access.IsActive, &access.AccessCount,
		&access.LastAccessedAt, &access.CreatedAt, &access.UpdatedAt,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("access token not found")
		}
		return nil, fmt.Errorf("failed to get customer collection access: %w", err)
	}

	return &access, nil
}

// GetCustomerCollectionAccessesByCollection retrieves all access tokens for a collection
func (s *CustomerCollectionAccessService) GetCustomerCollectionAccessesByCollection(collectionID uuid.UUID) ([]*models.CustomerCollectionAccess, error) {
	query := `
		SELECT cca.id, cca.collection_id, cca.customer_id, cca.access_token,
			   cca.customer_phone, cca.expires_at, cca.is_active, cca.access_count,
			   cca.last_accessed_at, cca.created_at, cca.updated_at,
			   c.name as customer_name, c.email as customer_email
		FROM customer_collection_access cca
		JOIN customers c ON cca.customer_id = c.id
		WHERE cca.collection_id = $1
		ORDER BY cca.created_at DESC
	`

	rows, err := s.db.Postgres.Query(query, collectionID)
	if err != nil {
		return nil, fmt.Errorf("failed to query customer collection accesses: %w", err)
	}
	defer rows.Close()

	var accesses []*models.CustomerCollectionAccess
	for rows.Next() {
		var access models.CustomerCollectionAccess
		var customerName, customerEmail string

		err := rows.Scan(
			&access.ID, &access.CollectionID, &access.CustomerID, &access.AccessToken,
			&access.CustomerPhone, &access.ExpiresAt, &access.IsActive, &access.AccessCount,
			&access.LastAccessedAt, &access.CreatedAt, &access.UpdatedAt,
			&customerName, &customerEmail,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan customer collection access: %w", err)
		}

		// Add customer info
		access.Customer = &models.Customer{
			ID:    access.CustomerID,
			Name:  customerName,
			Email: customerEmail,
			Phone: access.CustomerPhone,
		}

		accesses = append(accesses, &access)
	}

	return accesses, nil
}

// IncrementAccessCount increments the access count and updates last accessed time
func (s *CustomerCollectionAccessService) IncrementAccessCount(token string) error {
	query := `
		UPDATE customer_collection_access 
		SET access_count = access_count + 1, 
			last_accessed_at = CURRENT_TIMESTAMP,
			updated_at = CURRENT_TIMESTAMP
		WHERE access_token = $1
	`
	_, err := s.db.Postgres.Exec(query, token)
	if err != nil {
		return fmt.Errorf("failed to increment access count: %w", err)
	}
	return nil
}

// RevokeCustomerCollectionAccess deactivates an access token
func (s *CustomerCollectionAccessService) RevokeCustomerCollectionAccess(accessID uuid.UUID) error {
	query := `
		UPDATE customer_collection_access 
		SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP
		WHERE id = $1
	`
	_, err := s.db.Postgres.Exec(query, accessID)
	if err != nil {
		return fmt.Errorf("failed to revoke customer collection access: %w", err)
	}
	return nil
}
