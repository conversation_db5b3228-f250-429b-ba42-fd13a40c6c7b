package router

import (
	"context"
	"log"
	"net/http"

	"github.com/anandjewels/jewelry-backend/internal/database"
	"github.com/anandjewels/jewelry-backend/internal/handlers"
	"github.com/anandjewels/jewelry-backend/internal/middleware"
	"github.com/anandjewels/jewelry-backend/internal/services"
	"github.com/gin-gonic/gin"
)

// SetupRouter configures and returns the main router
func SetupRouter(db *database.DB) *gin.Engine {
	// Create router
	r := gin.New()

	// Add middleware
	r.Use(middleware.RequestLogger())
	r.Use(middleware.ErrorHandler())
	r.Use(middleware.CORS())

	// Image optimization middleware
	r.Use(middleware.ImageCacheMiddleware())
	r.Use(middleware.ImageCompressionMiddleware())
	r.Use(middleware.LazyLoadingMiddleware())
	r.Use(middleware.DeviceOptimizationMiddleware())
	r.Use(middleware.QualityOptimizationMiddleware())

	// Root redirect to API documentation
	r.GET("/", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/api/docs")
	})

	// Health check endpoints
	r.GET("/health", func(c *gin.Context) {
		// Check PostgreSQL connection
		if err := db.Postgres.Ping(); err != nil {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"status":   "unhealthy",
				"service":  "jewelry-backend",
				"version":  "1.0.0",
				"database": "disconnected",
				"error":    err.Error(),
			})
			return
		}

		// Check Redis connection (optional)
		redisStatus := "connected"
		if db.Redis == nil {
			redisStatus = "disabled"
		} else {
			ctx := context.Background()
			if err := db.Redis.Ping(ctx).Err(); err != nil {
				redisStatus = "disconnected"
			}
		}

		c.JSON(http.StatusOK, gin.H{
			"status":   "healthy",
			"service":  "jewelry-backend",
			"version":  "1.0.0",
			"database": "connected",
			"redis":    redisStatus,
		})
	})

	// API status endpoint
	r.GET("/api/v1/status", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message":  "Jewelry E-Commerce API is running",
			"version":  "1.0.0",
			"database": "PostgreSQL",
			"cache":    "Redis",
		})
	})

	// Database info endpoint
	r.GET("/api/v1/db-info", func(c *gin.Context) {
		var version string
		err := db.Postgres.QueryRow("SELECT version()").Scan(&version)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to query database version",
			})
			return
		}

		ctx := context.Background()
		redisInfo := db.Redis.Info(ctx, "server").Val()

		c.JSON(http.StatusOK, gin.H{
			"postgres_version": version,
			"redis_info":       redisInfo,
		})
	})

	// Initialize handlers
	productHandler := handlers.NewProductHandler(db)
	collectionHandler := handlers.NewCollectionHandler(db)
	customerCollectionAccessHandler := handlers.NewCustomerCollectionAccessHandler(db)
	customerHandler := handlers.NewCustomerHandler(db)
	orderHandler := handlers.NewOrderHandler(db)
	inventoryHandler := handlers.NewInventoryHandler(db)
	activityHandler := handlers.NewActivityHandler(db)

	// Initialize auth handler and service
	authHandler, err := handlers.NewAuthHandler(db)
	var authService *services.AuthService
	if err != nil {
		log.Printf("Warning: Authentication disabled due to configuration error: %v", err)
		authHandler = nil
		authService = nil
	} else {
		authService, err = services.NewAuthService(db)
		if err != nil {
			log.Printf("Warning: Authentication service initialization failed: %v", err)
			authHandler = nil
			authService = nil
		}
	}

	uploadHandler, err := handlers.NewUploadHandler(db)
	if err != nil {
		log.Printf("Warning: Upload handler initialization failed: %v", err)
		// Continue without upload functionality
	}

	docsHandler := handlers.NewDocsHandler()
	imageServingHandler := handlers.NewImageServingHandler(db)
	imageProxyHandler := handlers.NewImageProxyHandler(db)

	// API v1 routes
	v1 := r.Group("/api/v1")
	{
		// Authentication routes (public)
		if authHandler != nil {
			auth := v1.Group("/auth")
			{
				auth.GET("/google/login", authHandler.GoogleLogin)
				auth.GET("/google/callback", authHandler.GoogleCallback)
				auth.POST("/logout", authHandler.Logout)

				// Protected auth routes
				if authService != nil {
					authProtected := auth.Group("")
					authProtected.Use(middleware.AuthMiddleware(authService))
					{
						authProtected.GET("/me", authHandler.Me)
						authProtected.POST("/refresh", authHandler.RefreshToken)
					}
				}
			}
		}
		// Product routes (admin protected)
		products := v1.Group("/products")
		if authService != nil {
			products.Use(middleware.AuthMiddleware(authService))
			products.Use(middleware.AdminOnlyMiddleware())
			{
				products.POST("", productHandler.CreateProduct)
				products.GET("", productHandler.GetProducts)
				products.GET("/:id", productHandler.GetProduct)
				products.PUT("/:id", productHandler.UpdateProduct)
				products.PATCH("/:id/status", productHandler.UpdateProductStatus)
				products.DELETE("/:id", productHandler.DeleteProduct)

				// Image upload routes (if upload handler is available)
				if uploadHandler != nil {
					products.POST("/:id/images", uploadHandler.UploadProductImage)
					products.GET("/:id/images/:image_id", uploadHandler.GetImageInfo)
					products.DELETE("/:id/images/:image_id", uploadHandler.DeleteProductImage)
				}
			}
		}

		// Collection routes (mixed: some public, some admin)
		collections := v1.Group("/collections")
		{
			// Public collection routes (for customers)
			collections.GET("", collectionHandler.GetCollections)
			collections.GET("/:id", collectionHandler.GetCollection)
			collections.GET("/:id/products", collectionHandler.GetCollectionWithProducts)
			collections.GET("/slug/:slug", collectionHandler.GetCollectionBySlug)

			// Customer-specific collection access (public)
			collections.GET("/shared/:token", customerCollectionAccessHandler.GetCollectionByAccessToken)
		}

		// Admin-only collection routes
		if authService != nil {
			collectionsAdmin := v1.Group("/collections")
			collectionsAdmin.Use(middleware.AuthMiddleware(authService))
			collectionsAdmin.Use(middleware.AdminOnlyMiddleware())
			{
				collectionsAdmin.POST("", collectionHandler.CreateCollection)
				collectionsAdmin.PUT("/:id", collectionHandler.UpdateCollection)
				collectionsAdmin.PATCH("/:id/status", collectionHandler.UpdateCollectionStatus)
				collectionsAdmin.DELETE("/:id", collectionHandler.DeleteCollection)
				collectionsAdmin.POST("/:id/products", collectionHandler.AddProductToCollection)
				collectionsAdmin.DELETE("/:id/products/:product_id", collectionHandler.RemoveProductFromCollection)

				// Customer collection access management (admin only)
				collectionsAdmin.POST("/:id/customer-access", customerCollectionAccessHandler.CreateCustomerCollectionAccess)
				collectionsAdmin.GET("/:id/customer-access", customerCollectionAccessHandler.GetCustomerCollectionAccesses)
				collectionsAdmin.DELETE("/:id/customer-access/:accessId", customerCollectionAccessHandler.RevokeCustomerCollectionAccess)
			}
		}

		// Customer routes (mixed: creation public, management admin)
		customers := v1.Group("/customers")
		{
			// Public route for customer creation (from orders)
			customers.POST("", customerHandler.CreateCustomer)
		}

		// Admin-only customer management routes
		if authService != nil {
			customersAdmin := v1.Group("/customers")
			customersAdmin.Use(middleware.AuthMiddleware(authService))
			customersAdmin.Use(middleware.AdminOnlyMiddleware())
			{
				customersAdmin.GET("", customerHandler.GetCustomers)
				customersAdmin.GET("/:id", customerHandler.GetCustomer)
				customersAdmin.PUT("/:id", customerHandler.UpdateCustomer)
			}
		}

		// Order routes (mixed: creation public, management admin)
		orders := v1.Group("/orders")
		{
			// Public route for order creation (from customers)
			orders.POST("", orderHandler.CreateOrder)
		}

		// Admin-only order management routes
		if authService != nil {
			ordersAdmin := v1.Group("/orders")
			ordersAdmin.Use(middleware.AuthMiddleware(authService))
			ordersAdmin.Use(middleware.AdminOnlyMiddleware())
			{
				ordersAdmin.GET("", orderHandler.GetOrders)
				ordersAdmin.GET("/:id", orderHandler.GetOrder)
				ordersAdmin.PUT("/:id/status", orderHandler.UpdateOrderStatus)
			}
		}

		// Inventory routes (admin only)
		if authService != nil {
			inventory := v1.Group("/inventory")
			inventory.Use(middleware.AuthMiddleware(authService))
			inventory.Use(middleware.AdminOnlyMiddleware())
			{
				inventory.GET("", inventoryHandler.GetInventoryStatus)
				inventory.PUT("/:id", inventoryHandler.UpdateInventory)
				inventory.GET("/logs", inventoryHandler.GetInventoryLogs)
				inventory.GET("/alerts/low-stock", inventoryHandler.GetLowStockAlerts)
				inventory.GET("/alerts/out-of-stock", inventoryHandler.GetOutOfStockAlerts)
			}
		}

		// Activity routes (admin only)
		if authService != nil {
			activities := v1.Group("/activities")
			activities.Use(middleware.AuthMiddleware(authService))
			activities.Use(middleware.AdminOnlyMiddleware())
			{
				activities.GET("", activityHandler.GetActivities)
				activities.POST("", activityHandler.CreateActivity)
				activities.GET("/recent", activityHandler.GetRecentActivities)
			}
		}

		// Upload routes (admin only if upload handler is available)
		if uploadHandler != nil && authService != nil {
			upload := v1.Group("/upload")
			upload.Use(middleware.AuthMiddleware(authService))
			upload.Use(middleware.AdminOnlyMiddleware())
			{
				upload.POST("/image", uploadHandler.UploadImage)
				upload.GET("/health", uploadHandler.HealthCheck)
			}
		} else if uploadHandler != nil {
			// Upload available but auth disabled - still allow for development
			upload := v1.Group("/upload")
			{
				upload.POST("/image", uploadHandler.UploadImage)
				upload.GET("/health", uploadHandler.HealthCheck)
			}
		} else {
			// Add a diagnostic endpoint when upload is disabled
			upload := v1.Group("/upload")
			{
				upload.GET("/status", func(c *gin.Context) {
					c.JSON(http.StatusServiceUnavailable, gin.H{
						"error":   "upload_disabled",
						"message": "Image upload functionality is disabled due to configuration issues",
						"details": "Please check MinIO configuration (MINIO_ENDPOINT, MINIO_ACCESS_KEY, MINIO_SECRET_KEY, MINIO_BUCKET_NAME)",
					})
				})
			}
		}

		// Image serving routes (optimization)
		images := v1.Group("/images")
		{
			images.GET("/:id/:image_id/optimized", imageServingHandler.GetOptimizedImageURL)
			images.GET("/:id/:image_id/serve", imageServingHandler.ServeOptimizedImage)
			images.GET("/:id/set", imageServingHandler.GetProductImageSet)
			images.GET("/placeholder", imageServingHandler.GetImagePlaceholder)
		}

		// Image proxy routes (direct serving with optimization)
		proxy := v1.Group("/proxy")
		{
			proxy.GET("/health", imageProxyHandler.HealthCheck)
			proxy.GET("/images/:id/:image_id", imageProxyHandler.ProxyProductImage)
			proxy.GET("/images/:id/:image_id/info", imageProxyHandler.GetImageInfo)
		}
	}

	// Documentation routes
	docs := r.Group("/api/docs")
	{
		docs.GET("", docsHandler.RedirectToDocs)
		docs.GET("/", docsHandler.ServeSwaggerUI)
		docs.GET("/spec.yaml", docsHandler.ServeOpenAPISpec)
		docs.GET("/spec.json", docsHandler.ServeOpenAPIJSON)
	}

	// Handle 404
	r.NoRoute(middleware.NotFoundHandler())
	r.NoMethod(middleware.MethodNotAllowedHandler())

	return r
}
