import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  XMarkIcon,
  UserIcon,
  LinkIcon,
  ClockIcon,
  EyeIcon,
  TrashIcon,
  PlusIcon,
} from '@heroicons/react/24/outline';
import {
  getCustomers,
  createCustomerCollectionAccess,
  getCustomerCollectionAccesses,
  revokeCustomerCollectionAccess,
  type CreateCustomerCollectionAccessRequest,
  type CustomerCollectionAccess,
} from '../../lib/api';
import { useToast } from '../../hooks/useToast';
import ToastContainer from '../common/ToastContainer';

const accessSchema = z.object({
  customer_id: z.string().min(1, 'Customer is required'),
  customer_phone: z.string().min(1, 'Customer phone is required'),
  expires_at_date: z.string().optional(),
  expires_at_time: z.string().optional(),
});

type AccessFormData = z.infer<typeof accessSchema>;

interface CustomerAccessModalProps {
  collectionId: string;
  collectionName: string;
  isOpen: boolean;
  onClose: () => void;
}

const CustomerAccessModal: React.FC<CustomerAccessModalProps> = ({
  collectionId,
  collectionName,
  isOpen,
  onClose,
}) => {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null);
  const queryClient = useQueryClient();
  const { toasts, addToast, removeToast } = useToast();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = useForm<AccessFormData>({
    resolver: zodResolver(accessSchema),
  });

  const watchCustomerId = watch('customer_id');

  // Fetch customers for selection
  const { data: customersData } = useQuery({
    queryKey: ['customers'],
    queryFn: () => getCustomers({ limit: 100 }),
    enabled: isOpen,
  });

  // Fetch existing access tokens
  const { data: accessTokens, refetch: refetchAccess } = useQuery({
    queryKey: ['customer-collection-access', collectionId],
    queryFn: () => getCustomerCollectionAccesses(collectionId),
    enabled: isOpen,
  });

  // Create access mutation
  const createAccessMutation = useMutation({
    mutationFn: (data: CreateCustomerCollectionAccessRequest) =>
      createCustomerCollectionAccess(collectionId, data),
    onSuccess: () => {
      addToast({
        message: 'Customer access created successfully!',
        type: 'success',
      });
      refetchAccess();
      setShowCreateForm(false);
      reset();
      setSelectedCustomer(null);
    },
    onError: (error: any) => {
      addToast({
        message: error?.response?.data?.message || 'Failed to create customer access',
        type: 'error',
      });
    },
  });

  // Revoke access mutation
  const revokeAccessMutation = useMutation({
    mutationFn: (accessId: string) =>
      revokeCustomerCollectionAccess(collectionId, accessId),
    onSuccess: () => {
      addToast({
        message: 'Customer access revoked successfully!',
        type: 'success',
      });
      refetchAccess();
    },
    onError: (error: any) => {
      addToast({
        message: error?.response?.data?.message || 'Failed to revoke customer access',
        type: 'error',
      });
    },
  });

  // Update customer phone when customer is selected
  useEffect(() => {
    if (watchCustomerId && customersData?.data) {
      const customer = customersData.data.find(c => c.id === watchCustomerId);
      if (customer) {
        setSelectedCustomer(customer);
        setValue('customer_phone', customer.phone);
      }
    }
  }, [watchCustomerId, customersData, setValue]);

  const handleCreateAccess = (data: AccessFormData) => {
    let expires_at: string | undefined;

    if (data.expires_at_date && data.expires_at_time) {
      expires_at = `${data.expires_at_date}T${data.expires_at_time}:00.000Z`;
    } else if (data.expires_at_date) {
      expires_at = `${data.expires_at_date}T23:59:59.000Z`;
    }

    createAccessMutation.mutate({
      customer_id: data.customer_id,
      customer_phone: data.customer_phone,
      expires_at,
    });
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      addToast({
        message: 'Link copied to clipboard!',
        type: 'success',
      });
    });
  };

  const generateShareURL = (token: string) => {
    // Point to the customer portal (frontend) URL
    const frontendURL = import.meta.env.VITE_FRONTEND_URL || 'http://localhost:5174';
    return `${frontendURL}/collections/shared/${token}`;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Customer Access</h2>
            <p className="text-sm text-gray-500 mt-1">
              Generate 4-digit access codes for customers to view "{collectionName}"
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Create Access Button */}
          <div className="mb-6">
            <button
              onClick={() => setShowCreateForm(!showCreateForm)}
              className="btn-primary"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Generate Access Code
            </button>
          </div>

          {/* Create Form */}
          {showCreateForm && (
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Generate New Customer Access Code
              </h3>
              <form onSubmit={handleSubmit(handleCreateAccess)} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="form-label">Customer *</label>
                    <select className="form-input" {...register('customer_id')}>
                      <option value="">Select a customer</option>
                      {customersData?.data?.map((customer) => (
                        <option key={customer.id} value={customer.id}>
                          {customer.name} ({customer.phone})
                        </option>
                      ))}
                    </select>
                    {errors.customer_id && (
                      <p className="form-error">{errors.customer_id.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="form-label">Customer Phone *</label>
                    <input
                      type="text"
                      className="form-input"
                      {...register('customer_phone')}
                      readOnly={!!selectedCustomer}
                    />
                    {errors.customer_phone && (
                      <p className="form-error">{errors.customer_phone.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="form-label">Expiry Date (Optional)</label>
                    <input
                      type="date"
                      className="form-input"
                      {...register('expires_at_date')}
                    />
                  </div>

                  <div>
                    <label className="form-label">Expiry Time (Optional)</label>
                    <input
                      type="time"
                      className="form-input"
                      {...register('expires_at_time')}
                    />
                  </div>
                </div>

                <div className="flex space-x-3">
                  <button
                    type="submit"
                    disabled={createAccessMutation.isPending}
                    className="btn-primary disabled:opacity-50"
                  >
                    {createAccessMutation.isPending ? 'Creating...' : 'Generate Code'}
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setShowCreateForm(false);
                      reset();
                      setSelectedCustomer(null);
                    }}
                    className="btn-outline"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* Existing Access Tokens */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Existing Customer Access Codes
            </h3>
            
            {accessTokens && accessTokens.length > 0 ? (
              <div className="space-y-4">
                {accessTokens.map((access) => (
                  <div
                    key={access.id}
                    className="border rounded-lg p-4 bg-white"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <UserIcon className="h-4 w-4 text-gray-400" />
                          <span className="font-medium text-gray-900">
                            {access.customer?.name || 'Unknown Customer'}
                          </span>
                          <span className="text-sm text-gray-500">
                            ({access.customer_phone})
                          </span>
                          <span
                            className={`badge ${
                              access.is_active ? 'badge-success' : 'badge-error'
                            }`}
                          >
                            {access.is_active ? 'Active' : 'Inactive'}
                          </span>
                          <div className="ml-auto">
                            <span className="text-xs text-gray-500">Access Code:</span>
                            <span className="ml-1 font-mono text-lg font-bold text-blue-600 bg-blue-50 px-2 py-1 rounded">
                              {access.access_token}
                            </span>
                          </div>
                        </div>
                        
                        <div className="text-sm text-gray-600 space-y-1">
                          <div className="flex items-center space-x-2">
                            <LinkIcon className="h-4 w-4" />
                            <div className="flex-1">
                              <div className="text-xs text-gray-500 mb-1">Share this link with customer:</div>
                              <code className="bg-gray-100 px-2 py-1 rounded text-xs break-all">
                                {generateShareURL(access.access_token)}
                              </code>
                            </div>
                            <button
                              onClick={() => copyToClipboard(generateShareURL(access.access_token))}
                              className="text-blue-600 hover:text-blue-700 flex-shrink-0"
                              title="Copy link"
                            >
                              <LinkIcon className="h-4 w-4" />
                            </button>
                          </div>
                          
                          <div className="flex items-center space-x-4 text-xs">
                            <div className="flex items-center space-x-1">
                              <EyeIcon className="h-3 w-3" />
                              <span>{access.access_count} views</span>
                            </div>
                            {access.expires_at && (
                              <div className="flex items-center space-x-1">
                                <ClockIcon className="h-3 w-3" />
                                <span>
                                  Expires: {new Date(access.expires_at).toLocaleDateString()}
                                </span>
                              </div>
                            )}
                            {access.last_accessed_at && (
                              <div>
                                Last accessed: {new Date(access.last_accessed_at).toLocaleDateString()}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex space-x-2">
                        <button
                          onClick={() => revokeAccessMutation.mutate(access.id)}
                          disabled={revokeAccessMutation.isPending}
                          className="text-red-600 hover:text-red-700 disabled:opacity-50"
                          title="Revoke access"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <UserIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No customer access codes created yet.</p>
                <p className="text-sm">Generate a 4-digit code to share this collection with specific customers.</p>
              </div>
            )}
          </div>
        </div>

        {/* Toast notifications */}
        <ToastContainer toasts={toasts} onRemoveToast={removeToast} />
      </div>
    </div>
  );
};

export default CustomerAccessModal;
