import axios from 'axios';
import type {
  Product,
  Collection,
  Customer,
  Order,
  InventoryStatus,
  CreateProductRequest,
  CreateCollectionRequest,
  UpdateCollectionRequest,
  CreateCustomerRequest,
  CreateOrderRequest,
  PaginatedResponse,
  FilterOptions,
  DashboardStats,
  Activity,
  ActivityListRequest,
} from '../types';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:8080/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth tokens
api.interceptors.request.use(
  (config) => {
    // Add auth token from localStorage
    const token = localStorage.getItem('jwt_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Ensure we have proper headers
    if (!config.headers['Content-Type'] && !(config.data instanceof FormData)) {
      config.headers['Content-Type'] = 'application/json';
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('jwt_token');

      // Check if we're dealing with a whitelist error
      const errorData = error.response?.data;
      if (errorData?.error === 'user_not_authorized' ||
          errorData?.message?.includes('not authorized')) {
        // Redirect to unauthorized page
        window.location.href = '/unauthorized';
      } else {
        // Redirect to login page for other auth errors
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

// Health Check
export const healthCheck = async () => {
  const response = await api.get('/health');
  return response.data;
};

// Dashboard Stats
export const getDashboardStats = async (): Promise<DashboardStats> => {
  const [products, collections, orders, customers, pendingOrders, lowStockAlerts] = await Promise.all([
    getProducts({ limit: 1 }),
    getCollections({ limit: 1 }),
    getOrders({ limit: 1 }),
    getCustomers({ limit: 1 }),
    getOrders({ status: 'pending', limit: 1 }),
    getLowStockAlerts(),
  ]);

  return {
    total_products: products.total,
    total_collections: collections.total,
    total_orders: orders.total,
    total_customers: customers.total,
    pending_orders: pendingOrders.total,
    low_stock_products: lowStockAlerts.length,
    revenue_today: 0, // This would come from a dedicated revenue endpoint
    revenue_month: 0, // This would come from a dedicated revenue endpoint
  };
};

// Unified Search
export interface UnifiedSearchResult {
  type: 'product' | 'collection' | 'customer' | 'order';
  id: string;
  title: string;
  subtitle?: string;
  description?: string;
  image_url?: string;
  url: string;
  metadata?: Record<string, any>;
}

export interface UnifiedSearchResponse {
  results: UnifiedSearchResult[];
  total: number;
  query: string;
}

export const unifiedSearch = async (query: string, limit: number = 10): Promise<UnifiedSearchResponse> => {
  if (!query.trim()) {
    return { results: [], total: 0, query };
  }

  try {
    // Search across different entity types in parallel
    const [productsData, collectionsData, customersData, ordersData] = await Promise.all([
      getProducts({ search: query, limit: Math.ceil(limit / 4) }).catch(() => ({ data: [], total: 0 })),
      getCollections({ search: query, limit: Math.ceil(limit / 4) }).catch(() => ({ data: [], total: 0 })),
      getCustomers({ search: query, limit: Math.ceil(limit / 4) }).catch(() => ({ data: [], total: 0 })),
      getOrders({ search: query, limit: Math.ceil(limit / 4) }).catch(() => ({ data: [], total: 0 })),
    ]);

    const results: UnifiedSearchResult[] = [];

    // Transform products
    productsData.data.forEach(product => {
      results.push({
        type: 'product',
        id: product.id,
        title: product.name,
        subtitle: product.sku,
        description: product.description,
        image_url: product.images?.[0]?.image_url,
        url: `/products/${product.id}/edit`,
        metadata: {
          category: product.category,
          stock: product.stock_quantity,
          weight: product.weight,
        },
      });
    });

    // Transform collections
    collectionsData.data.forEach(collection => {
      results.push({
        type: 'collection',
        id: collection.id,
        title: collection.name,
        subtitle: collection.slug,
        description: collection.description,
        image_url: collection.cover_image_url,
        url: `/collections/${collection.id}/edit`,
        metadata: {
          is_public: collection.is_public,
          product_count: collection.product_count,
        },
      });
    });

    // Transform customers
    customersData.data.forEach(customer => {
      results.push({
        type: 'customer',
        id: customer.id,
        title: customer.name,
        subtitle: customer.email || 'No email',
        description: customer.phone,
        url: `/customers/${customer.id}/edit`,
        metadata: {
          total_orders: customer.total_orders,
          total_spent: customer.total_spent,
        },
      });
    });

    // Transform orders
    ordersData.data.forEach(order => {
      results.push({
        type: 'order',
        id: order.id,
        title: `Order #${order.order_number}`,
        subtitle: `${order.customer?.name || 'Unknown Customer'} - ${order.status}`,
        description: `$${order.total_amount} - ${new Date(order.created_at).toLocaleDateString()}`,
        url: `/orders/${order.id}`,
        metadata: {
          status: order.status,
          total_amount: order.total_amount,
          item_count: order.items?.length || 0,
        },
      });
    });

    // Sort results by relevance (exact matches first, then partial matches)
    const sortedResults = results.sort((a, b) => {
      const queryLower = query.toLowerCase();
      const aTitle = a.title.toLowerCase();
      const bTitle = b.title.toLowerCase();

      // Exact matches first
      if (aTitle === queryLower && bTitle !== queryLower) return -1;
      if (bTitle === queryLower && aTitle !== queryLower) return 1;

      // Starts with query
      if (aTitle.startsWith(queryLower) && !bTitle.startsWith(queryLower)) return -1;
      if (bTitle.startsWith(queryLower) && !aTitle.startsWith(queryLower)) return 1;

      // Alphabetical order
      return aTitle.localeCompare(bTitle);
    });

    return {
      results: sortedResults.slice(0, limit),
      total: productsData.total + collectionsData.total + customersData.total + ordersData.total,
      query,
    };
  } catch (error) {
    console.error('Unified search error:', error);
    return { results: [], total: 0, query };
  }
};

// Products API
export const getProducts = async (filters: FilterOptions = {}): Promise<PaginatedResponse<Product>> => {
  const params = new URLSearchParams();
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== '') {
      params.append(key, String(value));
    }
  });

  const response = await api.get(`/products?${params}`);
  return {
    data: response.data.products || [],
    total: response.data.total || 0,
    page: filters.page || 1,
    limit: filters.limit || 20,
    total_pages: Math.ceil((response.data.total || 0) / (filters.limit || 20)),
  };
};

export const getProduct = async (id: string): Promise<Product> => {
  const response = await api.get(`/products/${id}`);
  return response.data;
};

export const createProduct = async (data: CreateProductRequest): Promise<Product> => {
  const response = await api.post('/products', data);
  return response.data;
};

export const updateProduct = async (id: string, data: CreateProductRequest): Promise<Product> => {
  const response = await api.put(`/products/${id}`, data);
  return response.data;
};

export const deleteProduct = async (id: string): Promise<void> => {
  await api.delete(`/products/${id}`);
};

export const updateProductStatus = async (id: string, isActive: boolean): Promise<Product> => {
  const response = await api.patch(`/products/${id}/status`, { is_active: isActive });
  return response.data;
};

// Collections API
export const getCollections = async (filters: FilterOptions = {}): Promise<PaginatedResponse<Collection>> => {
  const params = new URLSearchParams();
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== '') {
      params.append(key, String(value));
    }
  });

  const response = await api.get(`/collections?${params}`);
  return {
    data: response.data.collections || [],
    total: response.data.total || 0,
    page: filters.page || 1,
    limit: filters.limit || 20,
    total_pages: Math.ceil((response.data.total || 0) / (filters.limit || 20)),
  };
};

export const getCollection = async (id: string): Promise<Collection> => {
  const response = await api.get(`/collections/${id}`);
  return response.data;
};

export const createCollection = async (data: CreateCollectionRequest): Promise<Collection> => {
  const response = await api.post('/collections', data);
  return response.data;
};

export const updateCollection = async (id: string, data: UpdateCollectionRequest): Promise<Collection> => {
  const response = await api.put(`/collections/${id}`, data);
  return response.data;
};

export const deleteCollection = async (id: string): Promise<void> => {
  await api.delete(`/collections/${id}`);
};

export const updateCollectionStatus = async (id: string, isActive: boolean): Promise<Collection> => {
  const response = await api.patch(`/collections/${id}/status`, { is_active: isActive });
  return response.data;
};

export const addProductToCollection = async (collectionId: string, productId: string, displayOrder: number = 0): Promise<void> => {
  await api.post(`/collections/${collectionId}/products`, {
    product_id: productId,
    display_order: displayOrder,
    is_featured: false,
  });
};

export const removeProductFromCollection = async (collectionId: string, productId: string): Promise<void> => {
  await api.delete(`/collections/${collectionId}/products/${productId}`);
};

// Customer Collection Access API
export interface CustomerCollectionAccess {
  id: string;
  collection_id: string;
  customer_id: string;
  access_token: string;
  customer_phone: string;
  expires_at?: string;
  is_active: boolean;
  access_count: number;
  last_accessed_at?: string;
  created_at: string;
  updated_at: string;
  collection?: Collection;
  customer?: Customer;
}

export interface CreateCustomerCollectionAccessRequest {
  customer_id: string;
  customer_phone: string;
  expires_at?: string;
}

export const createCustomerCollectionAccess = async (
  collectionId: string,
  data: CreateCustomerCollectionAccessRequest
): Promise<CustomerCollectionAccess> => {
  const response = await api.post(`/collections/${collectionId}/customer-access`, data);
  return response.data;
};

export const getCustomerCollectionAccesses = async (collectionId: string): Promise<CustomerCollectionAccess[]> => {
  const response = await api.get(`/collections/${collectionId}/customer-access`);
  return response.data;
};

export const revokeCustomerCollectionAccess = async (collectionId: string, accessId: string): Promise<void> => {
  await api.delete(`/collections/${collectionId}/customer-access/${accessId}`);
};

// Customers API
export const getCustomers = async (filters: FilterOptions = {}): Promise<PaginatedResponse<Customer>> => {
  const params = new URLSearchParams();
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== '') {
      params.append(key, String(value));
    }
  });

  const response = await api.get(`/customers?${params}`);
  return {
    data: response.data.customers || [],
    total: response.data.total || 0,
    page: filters.page || 1,
    limit: filters.limit || 20,
    total_pages: Math.ceil((response.data.total || 0) / (filters.limit || 20)),
  };
};

export const getCustomer = async (id: string): Promise<Customer> => {
  const response = await api.get(`/customers/${id}`);
  return response.data;
};

export const createCustomer = async (data: CreateCustomerRequest): Promise<Customer> => {
  const response = await api.post('/customers', data);
  return response.data;
};

export const updateCustomer = async (id: string, data: CreateCustomerRequest): Promise<Customer> => {
  const response = await api.put(`/customers/${id}`, data);
  return response.data;
};

// Orders API
export const getOrders = async (filters: FilterOptions = {}): Promise<PaginatedResponse<Order>> => {
  const params = new URLSearchParams();
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== '') {
      params.append(key, String(value));
    }
  });

  const response = await api.get(`/orders?${params}`);
  return {
    data: response.data.orders || [],
    total: response.data.total || 0,
    page: filters.page || 1,
    limit: filters.limit || 20,
    total_pages: Math.ceil((response.data.total || 0) / (filters.limit || 20)),
  };
};

export const getOrder = async (id: string): Promise<Order> => {
  const response = await api.get(`/orders/${id}`);
  return response.data;
};

export const createOrder = async (data: CreateOrderRequest): Promise<Order> => {
  const response = await api.post('/orders', data);
  return response.data;
};

export const updateOrderStatus = async (id: string, status: string): Promise<Order> => {
  const response = await api.put(`/orders/${id}/status`, { status });
  return response.data;
};

// Inventory API
export const getInventoryStatus = async (filters: FilterOptions = {}): Promise<PaginatedResponse<InventoryStatus>> => {
  const params = new URLSearchParams();
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== '') {
      params.append(key, String(value));
    }
  });

  const response = await api.get(`/inventory?${params}`);
  return {
    data: response.data.inventory || [],
    total: response.data.total || 0,
    page: filters.page || 1,
    limit: filters.limit || 20,
    total_pages: Math.ceil((response.data.total || 0) / (filters.limit || 20)),
  };
};

export const updateInventory = async (id: string, data: { quantity: number; change_type: string; reason: string }) => {
  const response = await api.put(`/inventory/${id}`, data);
  return response.data;
};

export const getLowStockAlerts = async () => {
  const response = await api.get('/inventory/alerts/low-stock');
  return response.data.alerts || [];
};

export const getOutOfStockAlerts = async () => {
  const response = await api.get('/inventory/alerts/out-of-stock');
  return response.data.alerts || [];
};

// Activities API
export const getRecentActivities = async (params: ActivityListRequest = {}): Promise<PaginatedResponse<Activity>> => {
  const searchParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== '') {
      searchParams.append(key, String(value));
    }
  });

  const response = await api.get(`/activities?${searchParams}`);
  return {
    data: response.data.activities || [],
    total: response.data.total || 0,
    page: response.data.page || 1,
    limit: response.data.limit || 20,
    total_pages: response.data.total_pages || 0,
  };
};

export default api;
